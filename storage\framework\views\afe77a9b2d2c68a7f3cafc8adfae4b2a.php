<!DOCTYPE html>
<html lang="zxx" class="no-js">
<head>
    <!-- Mobile Specific Meta -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Favicon-->
    <!-- <link rel="shortcut icon" href="img/fav.png"> -->
     <link rel="shortcut icon" href="<?php echo e(asset('theme/web/img/faviconl1.png')); ?>" sizes="48×48" type="image/png">
    <!-- Author Meta -->
    <meta name="author" content="codepixer">
    <!-- Meta Description -->
    <meta name="description" content="">
    <!-- Meta Keyword -->
    <meta name="keywords" content="">
    <!-- meta character set -->
    <meta charset="UTF-8">
    <!-- Site Title -->
    <title>Jobs-Recruitment-Employment-Career-Courses-Professionals</title>

    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,400,300,500,600,700" rel="stylesheet">
        <!--CSS------>
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/linearicons.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/font-awesome.min.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/bootstrap.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/magnific-popup.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/nice-select.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/animate.min.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/owl.carousel.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/main.css')); ?>">
            <link rel="stylesheet" href="<?php echo e(asset('theme/web/css/MembershipStyle.css')); ?>">

    <style>
        /* Force horizontal navigation - override main.css */
        div.nav2Align {
            display: block !important;
            text-align: center !important;
            padding: 10px 0 !important;
            margin: 0 !important;
        }

        ul.list-inline#navAlign {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
            align-items: center !important;
            list-style: none !important;
            margin: 0 auto !important;
            padding: 0 !important;
            width: 100% !important;
        }

        ul.list-inline#navAlign li.list-inline-item {
            display: inline-block !important;
            margin: 0 5px !important;
            list-style: none !important;
            float: none !important;
        }

        ul.list-inline#navAlign li.list-inline-item a {
            text-decoration: none !important;
            padding: 5px 10px !important;
            white-space: nowrap !important;
            display: inline-block !important;
        }

        /* Additional overrides for Bootstrap and main.css conflicts */
        .list-inline {
            padding-left: 0 !important;
            list-style: none !important;
        }

        .list-inline > li {
            display: inline-block !important;
            padding-right: 5px !important;
            padding-left: 5px !important;
        }

        /* Fix banner positioning */
        .banner-area {
            margin-top: -20px !important;
        }

        #bnr1 {
            min-height: 350px !important;
            height: auto !important;
            padding-top: 40px !important;
        }
    </style>

    </head>

    <body>
        <?php echo $__env->make('frontend.octopus.partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


        <?php echo $__env->yieldContent('content'); ?>

        <?php echo $__env->make('frontend.octopus.partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php echo $__env->make('frontend.octopus.partials.scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </body>
</html><?php /**PATH C:\laragon\www\jo\joboctopus_new\resources\views/frontend/octopus/layouts/master.blade.php ENDPATH**/ ?>
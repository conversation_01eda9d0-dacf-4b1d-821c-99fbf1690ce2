/*--------------------------- Color variations ----------------------*/
/* Medium Layout: 1280px */
/* Tablet Layout: 768px */
/* Mobile Layout: 320px */
/* Wide Mobile Layout: 480px */
/* =================================== */
/*  Basic Style 
/* =================================== */

.main-color{
    color:#004d99;
}

/* Advertise with us style */

.adPara>p{

    font-family: Arial;
    font-size: 14px;
    text-align: justify;
    line-height: 1.42857143;
    background-color: #fff;

}


@media (max-width:425px) 
{

#sMore{display:none;}

}

/*Styling from Main Page*/
#sMore {
    font-size: 17px;
    color: white !important;
    text-decoration: none;
    padding: 20px;
}

@media (max-width: 1024px){
  #sMore{
    font-size: 13px;
  }
}

/*addition responsive top nav 1 menu */





@media (min-width:1024px) {
  #navAlignft {
    display:none;
  }





}
/*End of top nav menu 1*/






.nav-tabs {
     border-bottom: 0px #ddd !important; 
    /* padding-bottom: 10px; */
    }

/* navigation tab list for mobile */
@media only screen and (max-width: 425px){
  .nav-tabs>li{
    margin: 3px;
    font-size: 12px;
    width: -webkit-fill-available;
  }
}
.tab-content > .tab-pane {
    margin-top: 20px;
}

a {
    color: #ffffff;
    text-decoration: none;
}

.nav > li > a {
    background-color: #337ab7;
    color: #FFFFFF;
}

.nav-tabs {
    border-bottom: 0px solid #dee2e6;
}


/*start post updated*/

#apply{
  margin-left:320px;
}

@media (max-width:1024px){
  #apply{
    margin-left:300px;

  }
}

@media (max-width:768px){
  #apply{
    margin-left:370px;
  }
}

@media (max-width:425px){
  #apply{
    margin-left:105px;
  }
}

@media (max-width:375px){
  #apply{
    margin-left:0px;
  }
}



/**/






/*End of Styling from Main Page*/

::-moz-selection {
  /* Code for Firefox */
  background-color: #004d99;
  color: #fff;
}

::selection {
  background-color: #004d99;
  color: #fff;
}

::-webkit-input-placeholder {
  /* WebKit, Blink, Edge */
  color: #777777;
  font-weight: 300;
}

:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #777777;
  opacity: 1;
  font-weight: 300;
}

::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #777777;
  opacity: 1;
  font-weight: 300;
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #777777;
  font-weight: 300;
}

::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #777777;
  font-weight: 300;
}

body {
  color: #777777;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.625em;
  position: relative;
}

ol, ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

select {
  display: block;
}

figure {
  margin: 0;
}

a {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

iframe {
  border: 0;
}

a, a:focus, a:hover {
  text-decoration: underline;
  color: #884dff;
  outline: 0;
}
.nav-menu>li>a:hover{text-decoration: underline; color: #884dff;font-weight:bold; }
.nav-menu>li>a.ticker-btn:hover{text-decoration: none; color:#e6005c;}
.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn.focus:active,
.btn:active:focus,
.btn:focus {
  text-decoration: none;
  outline: 0;
}

.card-panel {
  margin: 0;
  padding: 60px;
}

/**
 *  Typography
 *
 **/
.btn i, .btn-large i, .btn-floating i, .btn-large i, .btn-flat i {
  font-size: 1em;
  line-height: inherit;
}

.gray-bg {
  background: #f9f9ff;
}

h1, h2, h3,
h4, h5, h6 {
  font-family: "Poppins", sans-serif;
  color: #222222;
  line-height: 1.2em !important;
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 600;
}

.h1, .h2, .h3,
.h4, .h5, .h6 {
  margin-bottom: 0;
  margin-top: 0;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  color: #222222;
}

h1, .h1 {
  font-size: 36px;
}

h2, .h2 {
  font-size: 30px;
}

h3, .h3 {
  font-size: 24px;
}

h4, .h4 {
  font-size: 18px;
}

h5, .h5 {
  font-size: 16px;
}

h6, .h6 {
  font-size: 14px;
  color: #222222;
}

td, th {
  border-radius: 0px;
}

/**
 * For modern browsers
 * 1. The space content is one way to avoid an Opera bug when the
 *    contenteditable attribute is included anywhere else in the document.
 *    Otherwise it causes space to appear at the top and bottom of elements
 *    that are clearfixed.
 * 2. The use of `table` rather than `block` is only necessary if using
 *    `:before` to contain the top-margins of child elements.
 */
.clear::before, .clear::after {
  content: " ";
  display: table;
}

.clear::after {
  clear: both;
}

.fz-11 {
  font-size: 11px;
}

.fz-12 {
  font-size: 12px;
}

.fz-13 {
  font-size: 13px;
}

.fz-14 {
  font-size: 14px;
}

.fz-15 {
  font-size: 15px;
}

.fz-16 {
  font-size: 16px;
}

.fz-18 {
  font-size: 18px;
}

.fz-30 {
  font-size: 30px;
}

.fz-48 {
  font-size: 48px !important;
}

.fw100 {
  font-weight: 100;
}

.fw300 {
  font-weight: 300;
}

.fw400 {
  font-weight: 400 !important;
}

.fw500 {
  font-weight: 500;
}

.f700 {
  font-weight: 700;
}

.fsi {
  font-style: italic;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-120 {
  margin-top: 120px;
}

.mt-150 {
  margin-top: 150px;
}

.ml-0 {
  margin-left: 0 !important;
}

.ml-5 {
  margin-left: 25px !important;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-50 {
  margin-left: 50px;
}

.mr-0 {
  margin-right: 0 !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mr-15 {
  margin-right: 15px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-50 {
  margin-right: 50px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-0-i {
  margin-bottom: 0px !important;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-100 {
  margin-bottom: 100px;
}

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-150 {
  padding-top: 150px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pr-30 {
  padding-right: 30px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-90 {
  padding-left: 90px;
}

.p-40 {
  padding: 40px;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.text-italic {
  font-style: italic;
}

.text-white {
  color: #fff;
}

.text-black {
  color: #000;
}

.transition {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.section-full {
  padding: 100px 0;
}

.section-half {
  padding: 75px 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-rigth {
  text-align: right;
}

.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

.inline-flex {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.flex-grow {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -moz-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.flex-wrap {
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex-left {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  justify-content: flex-start;
}

.flex-middle {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}

.flex-right {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  justify-content: flex-end;
}

.flex-top {
  -webkit-align-self: flex-start;
  -moz-align-self: flex-start;
  -ms-flex-item-align: start;
  align-self: flex-start;
}

.flex-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}

.flex-bottom {
  -webkit-align-self: flex-end;
  -moz-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
}

.space-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  justify-content: space-between;
}

.space-around {
  -ms-flex-pack: distribute;
  -webkit-justify-content: space-around;
  -moz-justify-content: space-around;
  justify-content: space-around;
}

.flex-column {
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.flex-cell {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -moz-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.display-table {
  display: table;
}

.light {
  color: #fff;
}

.dark {
  color: #222;
}

.relative {
  position: relative;
}

.overflow-hidden {
  overflow: hidden;
}

.overlay {
  position: absolute;
  left: 0;
  right: 0;
  top:  0;
  bottom: 0;
}

/*Blue banner*/
.overlay1 {
  position: absolute;
  left: 0;
  right: 0;
  top:  0;
  bottom: 0;
}





.container.fullwidth {
  width: 100%;
}

.container.no-padding {
  padding-left: 0;
  padding-right: 0;
}

.no-padding {
  padding: 0;
}

.section-bg {
  background: #f9fafc;
}

@media (max-width: 767px) {
  .no-flex-xs {
    display: block !important;
  }
}

.row.no-margin {
  margin-left: 0;
  margin-right: 0;
}

.sample-text-area {
  background: #fff;
  padding: 100px 0 70px 0;
}

.text-heading {
  margin-bottom: 30px;
  font-size: 24px;
}

 sup, sub, u, del {
  color: #004d99;
}


b {
  color: #000000;
}

h1 {
  font-size: 36px;
}

h2 {
  font-size: 30px;
}

h3 {
  font-size: 24px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.5em;
}

.typography h1, .typography h2, .typography h3, .typography h4, .typography h5, .typography h6 {
  color: #777777;
}

.button-area {
  background: #fff;
}

.button-area .border-top-generic {
  padding: 70px 15px;
  border-top: 1px dotted #eee;
}

.button-group-area .genric-btn {
  margin-right: 10px;
  margin-top: 10px;
}

.button-group-area .genric-btn:last-child {
  margin-right: 0;
}

.genric-btn {
  display: inline-block;
  outline: none;
  line-height: 40px;
  padding: 0 30px;
  font-size: .8em;
  text-align: center;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.genric-btn:focus {
  outline: none;
}

.genric-btn.e-large {
  padding: 0 40px;
  line-height: 50px;
}

.genric-btn.large {
  line-height: 45px;
}

.genric-btn.medium {
  line-height: 30px;
}

.genric-btn.small {
  line-height: 25px;
}

.genric-btn.radius {
  border-radius: 3px;
}

.genric-btn.circle {
  border-radius: 20px;
}

.genric-btn.arrow {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.genric-btn.arrow span {
  margin-left: 10px;
}

.genric-btn.default {
  color: #222222;
  background: #f9f9ff;
  border: 1px solid transparent;
}

.genric-btn.default:hover {
  border: 1px solid #f9f9ff;
  background: #fff;
}

.genric-btn.default-border {
  border: 1px solid #f9f9ff;
  background: #fff;
}

.genric-btn.default-border:hover {
  color: #222222;
  background: #f9f9ff;
  border: 1px solid transparent;
}

.genric-btn.primary {
  color: #fff;
  background: #004d99;
  border: 1px solid transparent;
}

.genric-btn.primary:hover {
  color: #004d99;
  border: 1px solid #004d99;
  background: #fff;
}

.genric-btn.primary-border {
  color: #004d99;
  border: 1px solid #004d99;
  background: #fff;
}

.genric-btn.primary-border:hover {
  color: #fff;
  background: #004d99;
  border: 1px solid transparent;
}

.genric-btn.success {
  color: #fff;
  background: #4cd3e3;
  border: 1px solid transparent;
}

.genric-btn.success:hover {
  color: #4cd3e3;
  border: 1px solid #4cd3e3;
  background: #fff;
}

.genric-btn.success-border {
  color: #4cd3e3;
  border: 1px solid #4cd3e3;
  background: #fff;
}

.genric-btn.success-border:hover {
  color: #fff;
  background: #4cd3e3;
  border: 1px solid transparent;
}

.genric-btn.info {
  color: #fff;
  background: #38a4ff;
  border: 1px solid transparent;
}

.genric-btn.info:hover {
  color: #38a4ff;
  border: 1px solid #38a4ff;
  background: #fff;
}

.genric-btn.info-border {
  color: #38a4ff;
  border: 1px solid #38a4ff;
  background: #fff;
}

.genric-btn.info-border:hover {
  color: #fff;
  background: #38a4ff;
  border: 1px solid transparent;
}

.genric-btn.warning {
  color: #fff;
  background: #f4e700;
  border: 1px solid transparent;
}

.genric-btn.warning:hover {
  color: #f4e700;
  border: 1px solid #f4e700;
  background: #fff;
}

.genric-btn.warning-border {
  color: #f4e700;
  border: 1px solid #f4e700;
  background: #fff;
}

.genric-btn.warning-border:hover {
  color: #fff;
  background: #f4e700;
  border: 1px solid transparent;
}

.genric-btn.danger {
  color: #fff;
  background: #f44a40;
  border: 1px solid transparent;
}

.genric-btn.danger:hover {
  color: #f44a40;
  border: 1px solid #f44a40;
  background: #fff;
}

.genric-btn.danger-border {
  color: #f44a40;
  border: 1px solid #f44a40;
  background: #fff;
}

.genric-btn.danger-border:hover {
  color: #fff;
  background: #f44a40;
  border: 1px solid transparent;
}

.genric-btn.link {
  color: #222222;
  background: #f9f9ff;
  text-decoration: underline;
  border: 1px solid transparent;
}

.genric-btn.link:hover {
  color: #222222;
  border: 1px solid #f9f9ff;
  background: #fff;
}

.genric-btn.link-border {
  color: #222222;
  border: 1px solid #f9f9ff;
  background: #fff;
  text-decoration: underline;
}

.genric-btn.link-border:hover {
  color: #222222;
  background: #f9f9ff;
  border: 1px solid transparent;
}

.genric-btn.disable {
  color: #222222, 0.3;
  background: #f9f9ff;
  border: 1px solid transparent;
  cursor: not-allowed;
}

.generic-blockquote {
  padding: 30px 50px 30px 30px;
  background: #f9f9ff;
  border-left: 2px solid #004d99;
}

.progress-table-wrap {
  overflow-x: scroll;
}

.progress-table {
  background: #f9f9ff;
  padding: 15px 0px 30px 0px;
  min-width: 800px;
}

.progress-table .serial {
  width: 11.83%;
  padding-left: 30px;
}

.progress-table .country {
  width: 28.07%;
}

.progress-table .visit {
  width: 19.74%;
}

.progress-table .percentage {
  width: 40.36%;
  padding-right: 50px;
}

.progress-table .table-head {
  display: flex;
}

.progress-table .table-head .serial, .progress-table .table-head .country, .progress-table .table-head .visit, .progress-table .table-head .percentage {
  color: #222222;
  line-height: 40px;
  text-transform: uppercase;
  font-weight: 500;
}

.progress-table .table-row {
  padding: 15px 0;
  border-top: 1px solid #edf3fd;
  display: flex;
}

.progress-table .table-row .serial, .progress-table .table-row .country, .progress-table .table-row .visit, .progress-table .table-row .percentage {
  display: flex;
  align-items: center;
}

.progress-table .table-row .country img {
  margin-right: 15px;
}

.progress-table .table-row .percentage .progress {
  width: 80%;
  border-radius: 0px;
  background: transparent;
}

.progress-table .table-row .percentage .progress .progress-bar {
  height: 5px;
  line-height: 5px;
}

.progress-table .table-row .percentage .progress .progress-bar.color-1 {
  background-color: #6382e6;
}

.progress-table .table-row .percentage .progress .progress-bar.color-2 {
  background-color: #e66686;
}

.progress-table .table-row .percentage .progress .progress-bar.color-3 {
  background-color: #f09359;
}

.progress-table .table-row .percentage .progress .progress-bar.color-4 {
  background-color: #73fbaf;
}

.progress-table .table-row .percentage .progress .progress-bar.color-5 {
  background-color: #73fbaf;
}

.progress-table .table-row .percentage .progress .progress-bar.color-6 {
  background-color: #6382e6;
}

.progress-table .table-row .percentage .progress .progress-bar.color-7 {
  background-color: #a367e7;
}

.progress-table .table-row .percentage .progress .progress-bar.color-8 {
  background-color: #e66686;
}

.single-gallery-image {
  margin-top: 30px;
  background-repeat: no-repeat !important;
  background-position: center center !important;
  background-size: cover !important;
  height: 200px;
}

.list-style {
  width: 14px;
  height: 14px;
}

.unordered-list li {
  position: relative;
  padding-left: 30px;
  line-height: 1.82em !important;
}

.unordered-list li:before {
  content: "";
  position: absolute;
  width: 14px;
  height: 14px;
  border: 3px solid #004d99;
  background: #fff;
  top: 4px;
  left: 0;
  border-radius: 50%;
}

.ordered-list {
  margin-left: 30px;
}

.ordered-list li {
  list-style-type: decimal-leading-zero;
  color: #004d99;
  font-weight: 500;
  line-height: 1.82em !important;
}

.ordered-list li span {
  font-weight: 300;
  color: #777777;
}

.ordered-list-alpha li {
  margin-left: 30px;
  list-style-type: lower-alpha;
  color: #004d99;
  font-weight: 500;
  line-height: 1.82em !important;
}

.ordered-list-alpha li span {
  font-weight: 300;
  color: #777777;
}

.ordered-list-roman li {
  margin-left: 30px;
  list-style-type: lower-roman;
  color: #004d99;
  font-weight: 500;
  line-height: 1.82em !important;
}

.ordered-list-roman li span {
  font-weight: 300;
  color: #777777;
}

.single-input {
  display: block;
  width: 100%;
  line-height: 40px;
  border: none;
  outline: none;
  background: #f9f9ff;
  padding: 0 20px;
}

.single-input:focus {
  outline: none;
}

.input-group-icon {
  position: relative;
}

.input-group-icon .icon {
  position: absolute;
  left: 20px;
  top: 0;
  line-height: 40px;
  z-index: 3;
}

.input-group-icon .icon i {
  color: #797979;
}

.input-group-icon .single-input {
  padding-left: 45px;
}

.single-textarea {
  display: block;
  width: 100%;
  line-height: 40px;
  border: none;
  outline: none;
  background: #f9f9ff;
  padding: 0 20px;
  height: 100px;
  resize: none;
}

.single-textarea:focus {
  outline: none;
}

.single-input-primary {
  display: block;
  width: 100%;
  line-height: 40px;
  border: 1px solid transparent;
  outline: none;
  background: #f9f9ff;
  padding: 0 20px;
}

.single-input-primary:focus {
  outline: none;
  border: 1px solid #004d99;
}

.single-input-accent {
  display: block;
  width: 100%;
  line-height: 40px;
  border: 1px solid transparent;
  outline: none;
  background: #f9f9ff;
  padding: 0 20px;
}

.single-input-accent:focus {
  outline: none;
  border: 1px solid #eb6b55;
}

.single-input-secondary {
  display: block;
  width: 100%;
  line-height: 40px;
  border: 1px solid transparent;
  outline: none;
  background: #f9f9ff;
  padding: 0 20px;
}

.single-input-secondary:focus {
  outline: none;
  border: 1px solid #f09359;
}

.default-switch {
  width: 35px;
  height: 17px;
  border-radius: 8.5px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.default-switch input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.default-switch input + label {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #004d99;
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
  box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.default-switch input:checked + label {
  left: 19px;
}

.primary-switch {
  width: 35px;
  height: 17px;
  border-radius: 8.5px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.primary-switch input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.primary-switch input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.primary-switch input + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 8.5px;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}

.primary-switch input + label:after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #fff;
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
  box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.primary-switch input:checked + label:after {
  left: 19px;
}

.primary-switch input:checked + label:before {
  background: #004d99;
}

.confirm-switch {
  width: 35px;
  height: 17px;
  border-radius: 8.5px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.confirm-switch input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.confirm-switch input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.confirm-switch input + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 8.5px;
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
  cursor: pointer;
}

.confirm-switch input + label:after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #fff;
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
  box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.confirm-switch input:checked + label:after {
  left: 19px;
}

.confirm-switch input:checked + label:before {
  background: #4cd3e3;
}

.primary-checkbox {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.primary-checkbox input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.primary-checkbox input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.primary-checkbox input:checked + label {
  background: url(../img/elements/primary-check.png) no-repeat center center/cover;
  border: none;
}

.confirm-checkbox {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.confirm-checkbox input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.confirm-checkbox input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.confirm-checkbox input:checked + label {
  background: url(../img/elements/success-check.png) no-repeat center center/cover;
  border: none;
}

.disabled-checkbox {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.disabled-checkbox input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.disabled-checkbox input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.disabled-checkbox input:disabled {
  cursor: not-allowed;
  z-index: 3;
}

.disabled-checkbox input:checked + label {
  background: url(../img/elements/disabled-check.png) no-repeat center center/cover;
  border: none;
}

.primary-radio {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.primary-radio input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.primary-radio input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.primary-radio input:checked + label {
  background: url(../img/elements/primary-radio.png) no-repeat center center/cover;
  border: none;
}

.confirm-radio {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.confirm-radio input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.confirm-radio input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.confirm-radio input:checked + label {
  background: url(../img/elements/success-radio.png) no-repeat center center/cover;
  border: none;
}

.disabled-radio {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background: #f9f9ff;
  position: relative;
  cursor: pointer;
}

.disabled-radio input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.disabled-radio input + label {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #f1f1f1;
}

.disabled-radio input:disabled {
  cursor: not-allowed;
  z-index: 3;
}

.disabled-radio input:checked + label {
  background: url(../img/elements/disabled-radio.png) no-repeat center center/cover;
  border: none;
}

.default-select {
  height: 40px;

}



.default-select .nice-select {
  border: none;
  border-radius: 0px;
  height: 40px;
  background: #f9f9ff;
  padding-left: 20px;
  padding-right: 40px;
  
}

.default-select .nice-select .list {
  margin-top: 0;
  border: none;
  border-radius: 0px;
  box-shadow: none;
  width: 100%;
  padding: 10px 0 10px 0px;
}

.default-select .nice-select .list .option {
  font-weight: 300;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  line-height: 28px;
  min-height: 28px;
  font-size: 12px;
  padding-left: 20px;
}

.default-select .nice-select .list .option.selected {
  color: #0066cc;

  background: transparent;
}

.default-select .nice-select .list .option:hover {
  color: #e6005c;
  background: transparent;
}

.default-select .current {
  margin-right: 50px;
  font-weight: 300;
}

.default-select .nice-select::after {
  right: 20px;
}

.form-select {
  height: 40px;
  width: 100%;
}

.form-select .nice-select {
  border: none;
  border-radius: 0px;
  height: 40px;
  background: #f9f9ff;
  padding-left: 45px;
  padding-right: 40px;
  width: 100%;
}

.form-select .nice-select .list {
  margin-top: 0;
  border: none;
  border-radius: 0px;
  box-shadow: none;
  width: 100%;
  padding: 10px 0 10px 0px;
}

.form-select .nice-select .list .option {
  font-weight: 300;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  line-height: 28px;
  min-height: 28px;
  font-size: 12px;
  padding-left: 45px;
}

.form-select .nice-select .list .option.selected {
  color: #004d99;
  background: transparent;
}

.form-select .nice-select .list .option:hover {
  color: #004d99;
  background: transparent;
}

.form-select .current {
  margin-right: 50px;
  font-weight: 300;
}

.form-select .nice-select::after {
  right: 20px;
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#header {
  background-color: #fff;
  padding: 14px 0;
  position: relative;
  left: 0;
  top: 0;
  right: 0;
  transition: all 0.5s;
  z-index: 997;
}

/* #header.header-scrolled { */
  /* background: rgba(255, 255, 255, 255); */
  /* transition: all 0.5s; */
/* } */

@media (max-width: 673px) {
  #logo {
    margin-left: 20px;
  }
}

#header #logo h1 {
  font-size: 34px;
  margin: 0;
  padding: 0;
  line-height: 1;
  font-weight: 700;
  letter-spacing: 3px;
}

#header #logo h1 a, #header #logo h1 a:hover {
  color: #fff;
  padding-left: 10px;
  border-left: 4px solid #004d99;
}

#header #logo img {
  padding: 0;
  margin: 0;
}

@media (max-width: 768px) {
  #header #logo h1 {
    font-size: 28px;
  }
  #header #logo img {
    max-height: 60px;
  }
}

.ticker-btn {
	
	 background: #0066cc; 
  color: #fff !important;
  padding: 6px 20px !important;
  text-transform: uppercase;
}

.ticker-btn:hover {
	background: #ff5050;
  /* background: #e6005c; */
  color: #fff !important;
}

@media (max-width: 960px) {
  .ticker-btn {
    display: none !important;
  }
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Nav Menu Essentials */
.nav-menu, .nav-menu * {
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-menu ul {
  position: absolute;
  display: none;
  top: 100%;
  left: 0;
  z-index: 99;
}

.nav-menu li {
  position: relative;
  white-space: nowrap;
}

.nav-menu > li {
  float: left;
}

.nav-menu li:hover > ul,
.nav-menu li.sfHover > ul {
  display: block;
}

.nav-menu ul ul {
  top: 0;
  left: 100%;
}

.nav-menu ul li {
  min-width: 270px;
  min-height:10px;
  text-align: left;
}

.menu-has-children ul li a {
  padding: 4px 10px !important;
  width:10px;
  
}


#navAlign {
    margin-bottom: -20px;
    margin-top:-10px;
    margin-left: 125px;
}


#searchCasual{


  background-color:#fff; 
  color:grey; 
   border: 2px solid #D3D3D3; 
}


@media(max-width:780px )
{
 #navAlign{

  display:none;
 }

}


@media (min-width:2560px )and (max-width:2560px) 

{

  #navAlign{margin-left: 700px;}

}





@media (min-width:1024px) and (max-width:1024px )
{

#signUp{

     margin-left:50px;

}

}

@media (max-width:1024px) {

  
  #navAlign li>a{
      
          font-size:1em;
         
                  }



.nav-menu > li {
  margin-left: 10px;
}




}

.nav2Align{
   display:inline;
   padding-left:40px;
   margin:  -20px 0px 4px -90px;
}

#nav2Align{   
   margin: -15px 0px -15px 20px;
   text-align: center;
}


@media (max-width:780px) {
  #nav2Align {
    display:none;
  }
}


/*hr.new1 { display: block; margin-before: 0.25em; margin-after: 0.25em; margin-start: auto; margin-end: auto; overflow: hidden; border-style: inset; border-width: 0.5px;}
*/

hr.new1 { 
    display: block; 
    height: 5px;
    margin-bottom: 0px; 
    border-width: 0.5px;
}

@media (max-width:768px){
  hr.new1 { 
      display: block; 
      margin-bottom: 0px; 
      border-style: inset; 
      border-width: 0px;
      margin-top: 0px;
      border-top: 0px;
  }
}


/* Nav Menu Arrows */
.sf-arrows .sf-with-ul {
  padding-right: 30px;

}

.sf-arrows .sf-with-ul:after {
  content: "\f107";
  position: absolute;
  right: 15px;
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
}

.sf-arrows ul .sf-with-ul:after {
  content: "\f105";
}

/* Nav Meu Container */
#nav-menu-container {
  margin: 0px 0px 0px -80px;
}

@media (min-width: 1024px) {
  #nav-menu-container {
    padding-right: -1px;
  }



}

@media (max-width: 960px) {
  #nav-menu-container {
    display: none;
  }
}

/* Nav Meu Styling */
.nav-menu a {
  font-family: Verdana;
  padding: 6px 8px;
  text-decoration: none;
  display: inline-block;
  color: #666666;
  font-weight: 400;
  font-size: 12px;
  text-transform: uppercase;
  outline: none;
  
}

.nav-menu1 ul li a {
  padding: 10px;
  color: #333;
  transition: 0.3s;
  display: block;
  font-size: 12px;
  text-transform: none;
  
 
}

.nav-menu > li {
  margin-left: 10px;
}

.nav-menu ul {
  margin: 14px 0 0 0;
  padding: 10px;
  box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
  background: #fff;
}

.nav-menu ul li {
  transition: 0.3s;

}

.nav-menu ul li a {
  padding: 10px;
  color: #333;
  transition: 0.3s;
  display: block;
  font-size: 12px;
  text-transform: none;
  
 
}

.nav-menu ul li:hover > a {
  color: #004d99;
}

/* added banner nav hover*/
.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
  color: white !important;
  background-color: #ff0066 !important;
  cursor: default;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}

.nav-tabs>li>a:hover{
  background-color: #006080 !important;
}

.nav-tabs>a:hover{
  background-color: #006080 !important;
}

.nav-tabs>a:focus{
  background-color: #ff0066 !important;
}
/**/
.nav-menu ul ul {
  margin: 0;
}

/* Mobile Nav Toggle */
#mobile-nav-toggle {
  position: absolute;
  right: 15px;
  z-index: 999;
  top: 16px;
  border: 0;
  background: none;
  font-size: 24px;
  display: none;
  transition: all 0.4s;
  outline: none;
  cursor: pointer;
}

#mobile-nav-toggle i {
  color: #1a75ff;
  font-weight: 900;
}

@media (max-width: 960px) {
  #mobile-nav-toggle {
    display: inline;
  }
  #nav-menu-container {
    display: none;
  }
}

/* Mobile Nav Styling */
#mobile-nav {
  position: fixed;
  top: 0;
  padding-top: 18px;
  bottom: 0;
  z-index: 998;
  background: rgba(0, 0, 0, 0.8);
  left: -260px;
  width: 260px;
  overflow-y: auto;
  transition: 0.4s;
}

#mobile-nav ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

#mobile-nav ul li {
  position: relative;
}

#mobile-nav ul li a {
  color: #fff;
  font-size: 13px;
  text-transform: uppercase;
  overflow: hidden;
  padding: 10px 22px 10px 15px;
  position: relative;
  text-decoration: none;
  width: 100%;
  display: block;
  outline: none;
  font-weight: 700;
}

#mobile-nav ul li a:hover {
  color: #fff;
}

#mobile-nav ul li li {
  padding-left: 30px;
}

#mobile-nav ul .menu-has-children i {
  position: absolute;
  right: 0;
  z-index: 99;
  padding: 15px;
  cursor: pointer;
  color: #fff;
}

#mobile-nav ul .menu-has-children i.fa-chevron-up {
  color: #004d99;
}

#mobile-nav ul .menu-has-children li a {
  text-transform: capitalize;
}

#mobile-nav ul .menu-item-active {
  color: #004d99;
}

#mobile-body-overly {
  width: 100%;
  height: 100%;
  z-index: 997;
  top: 0;
  left: 0;
  position: fixed;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}

/* Mobile Nav body classes */
body.mobile-nav-active {
  overflow: hidden;
}

body.mobile-nav-active #mobile-nav {
  left: 0;
}

body.mobile-nav-active #mobile-nav-toggle {
  color: #fff;
}

.section-gap {
  padding: 25px 0;
}

.section-title {
  padding-bottom: 30px;
}

.section-title h2 {
  margin-bottom: 20px;
}

.section-title p {
  font-size: 16px;
  margin-bottom: 0;
}

@media (max-width: 991.98px) {
  .section-title p br {
    display: none;
  }
}

.p1-gradient-bg, .banner-area .overlay-bg, .sidebar .single-slidebar .cat-list li:hover, .callto-action-area, .single-price:hover .price-bottom, .single-service:hover, .submit-right, .submit-left, .contact-btns, .form-area .primary-btn {
  background-image: -moz-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -webkit-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -ms-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
}

.p1-gradient-bg, .banner-area1 .overlay-bg1, .sidebar .single-slidebar .cat-list li:hover, .callto-action-area, .single-price:hover .price-bottom, .single-service:hover, .submit-right, .submit-left, .contact-btns, .form-area .primary-btn {
  background-image: -moz-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -webkit-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -ms-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
}



.p1-gradient-bg, .banner-area2 .overlay-bg2, .sidebar .single-slidebar .cat-list li:hover, .callto-action-area, .single-price:hover .price-bottom, .single-service:hover, .submit-right, .submit-left, .contact-btns, .form-area .primary-btn {
  background-image: -moz-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -webkit-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -ms-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
}


 .single-service-recruitment:hover {
  background-image: -moz-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -webkit-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
  background-image: -ms-linear-gradient(0deg, #1a75ff 0%, #1a75ff 100%);
}


.p1-gradient-color, .feat-txt h1 {
  background: -moz-linear-gradient(0deg, #bfacff 0%, #0066ff 100%);
  background: -webkit-linear-gradient(0deg, #bfacff 0%, #0066ff 100%);
  background: -ms-linear-gradient(0deg, #bfacff 0%, #0066ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.primary-btn {
  background-color: #004d99;
  line-height: 42px;
  padding-left: 30px;
  padding-right: 60px;
  border-radius: 25px;
  border: none;
  color: #fff;
  display: inline-block;
  font-weight: 500;
  position: relative;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  cursor: pointer;
  text-transform: uppercase;
  position: relative;
}

.primary-btn:focus {
  outline: none;
}

.primary-btn span {
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-60%);
  right: 30px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.primary-btn:hover {
  color: #fff;
}

.primary-btn:hover span {
  color: #fff;
  right: 20px;
}

.primary-btn.white {
  border: 1px solid #fff;
  color: #fff;
}

.primary-btn.white span {
  color: #fff;
}

.primary-btn.white:hover {
  background: #fff;
  color: #004d99;
}

.primary-btn.white:hover span {
  color: #004d99;
}


/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 768px) {
  .banner-area {
      height: 230px !important;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }

.banner-area1 {
      height: 170px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }


  .banner-area2 {
      height: 170px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }



  .overlay {
      height: 230px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
  }

   .overlay1 {
      height: 150px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 10px;
  }

 .overlay2 {
      height: 150px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 10px;
  }


    /*.menu1 a{*/
        /*font-size: 10px;*/
    /*}*/
}

/* small phones*/
@media only screen and (min-width: 320px) {
  .banner-area {
    height: 230px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }

 .banner-area1 {
      height: 120px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }



.banner-area2 {
      height: 370px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }


  .overlay {
      height: 230px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
  }


   .overlay1 {
      height: 120px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

  }

  .overlay2 {
      height: 120px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

  }
    /*.menu1 a{*/
        /*font-size: 10px;*/
    /*}*/
}

.banner-area .overlay-bg {
  opacity: .70;
}

.banner-area1 .overlay-bg1 {
  opacity: .70;
}

.banner-area2 .overlay-bg2 {
  opacity: .70;
}


.banner-content {
  margin-top: 2px;
  text-align: center;
}

/*banner content margin for mobile device*/.header1{}

.header1{height: 30px !important;}

@media only screen and (max-width: 425PX){
  .banner-content{
    margin: 20px;
  }
}
/* to overlap and hide the feature job categories text */
@media only screen and (max-width: 768px) {
  .banner-content {
    z-index: 1;
  }
}

/* feature in mabile view*/
@media only screen and (max-width: 425px){
  .btn{
    width: -webkit-fill-available;
  }
}

@media only screen and (min-width: 1024px){
  #menu1 a{
    padding: 6px 25px;
  }
}

 .banner-area2 {
      height: 170px;
      background: url(../img/header-bg.jpg) center;
      background-size: cover;
  }


   .overlay2 {
      height: 170px !important;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 10px;
  }



@media only screen and (min-width: 1440px){
  #menu1 a{
    padding: 6px 35px;
  }
 
  }


.banner-content h1 {
  font-size: 48px;
  font-weight: 300;
  line-height: 1em;
  margin-top: 20px;
}

.banner-content h1 span {
  color: #004d99;
}

@media (max-width: 991.98px) {
  .banner-content h1 {
    font-size: 36px;
  }
}

@media (max-width: 991.98px) {
  .banner-content h1 br {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .banner-content h1 {
    font-size: 45px;
  }
}

@media (max-width: 414px) {
  .banner-content h1 {
    font-size: 32px;
  }
}

.banner-content p span {
  color: #004d99;
}

@media (max-width: 1280px) {
  .banner-content br {
    display: none;
  }
}


@media (min-width:1280px) {
  #nav2Align {
    display: none;
  }
}





.banner-content .form-wrap {
    /* background-color: #0066cc; */
	 background-color: #004d99;
   border: 1px solid #b8aaf3;
   padding: 15px 0;
   margin: -10x;
   /* margin: 40px 0 80px !important; */
   /*margin: center !important; */
  
  
}

.banner-content .form-wrap .nice-select {
  width: 100%;
}

.banner-content .form-wrap .form-control {
  height: 35px;
  border-radius: 0;
  font-size: 14px;
}

.banner-content .form-wrap .btn-info {
  background-color:#ff0066!important;
 /* background-color: #ff3333; */
  width: 100%;
  height: 80%;
  color: #fff;
  border-radius: 0;
  border: none;
  text-transform: uppercase;
}

.banner-content .form-wrap .btn-info {
  font-size: 14px;
}

@media (max-width: 960px) {
  .banner-content .form-cols {
    margin-bottom: 15px;
  }
  .banner-content .form-cols:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 768px ) and (min-width: 768px)
{
    .banner-content .form-wrap .btn-info{

      height:70%;
    }

     .banner-content .form-wrap{

      padding: 20px 0px;
     }

}


/* style for work location type */
input{
  width: 30px;
  height: 40px;
  padding-left: 10px;
} 

#checkhour{
  margin-left: 10px;
  height: 15px;
  width: auto;
}

#wl1{
  color: white;
  width: 185px;
}

@media only screen and (max-width: 320px){
  #checkall{
    margin-bottom: 8px;
  }
  #checkhour{
    margin:0px 3px 0px 10px;
  }
  #wl1{
    width: 200px;
    margin-bottom:2px;
  }
}

@media only screen and (min-width:350px) and (max-width: 375px){
  #wl1{
    width: 270px;
    margin-bottom:2px;
  }
  #checkall{
    margin-bottom: 8px;
  }
  #checkhour{
    margin:0px 3px 0px 10px;
  }
}

@media only screen and (min-width:400px) and (max-width: 425px){
  #wl1{
    width: 295px;
    margin-bottom:2px;
  }
  #checkall{
    margin-bottom: 8px;
  }
  #checkhour{
    margin:0px 3px 0px 10px;
  }
}

/* style for sort by date*/
#meeting{
  width:160px;
}
@media only screen and (max-width: 1024px){
  #meeting{
    width: 126px;
  }
}

@media only screen and (min-width: 600px) and (max-width: 768px){
  #menu3{
    padding: 0px 0px;
  }
  #meeting1{
    width: 200px;
  }
  #meeting2{
    width: 230px;
  }
  #meeting3{
    width: 200px;
  }
} 

@media only screen and (min-width: 400px) and (max-width: 425px){
  #meeting1{
    margin-bottom:10px;
    padding: 0px 124px;
  }
  #meeting2{
    width: 197px;
    padding:0px 0px 0px 10px;
  }
  #meeting3{
    width: 179px;
    padding:0px 0px 0px 10px;
  }
} 

@media only screen and (min-width:350px) and (max-width: 375px){
  #meeting1{
    margin-bottom:10px;
    padding: 0px 99px;
  }
  #meeting2{
    width: 130px;
    padding:0px;
  }
  #meeting3{
    width: 143px;
    padding:0px;
  }
} 

@media only screen and (max-width: 320px){
  #meeting1{
    margin-bottom:10px;
    padding: 0px 70px;
  }
  #meeting2{
    width: 130px;
    padding:0px 0px 0px 5px;
  }
  #meeting3{
    width: 143px;
    padding:0px;
  }
}

/* addistional style for srot by date*/
@media only screen and (min-width: 800px){
#menu3{
  padding: 0% 17%;
}
}
/* end of sort by date*/
.primary-btn {
  background: #fff;
  border: 1px solid transparent;
  color: #222;
  padding-left: 40px;
  padding-right: 40px;
  border-radius: 50px;
}

.primary-btn:hover {
  border: 1px solid #fff;
  background: transparent;
  color: #fff;
}

.popular-post-area .owl-dots {
  text-align: center;
  bottom: 5px;
  margin-top: 20px;
  width: 100%;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  backface-visibility: hidden;
}

.popular-post-area .owl-dot {
  height: 10px;
  width: 10px;
  display: inline-block;
  background: rgba(127, 127, 127, 0.5);
  margin-left: 5px;
  margin-right: 5px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.popular-post-area .owl-dot.active {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  background: #004d99;
}

@media (max-width: 960px) {
  .post-list {
    margin-bott

    om: 50px;
  }
}

.single-popular-post {
  background-color: #004d99;
  padding: 20px;
}

.single-popular-post .details {
  margin-left: 70px;
}

/* mobile view for size M & L*/
@media only screen and (max-width: 375px){
  .single-popular-post .details {
    margin-left: 80px;
  }
}

.single-popular-post .details p {
  margin-bottom: 0;
}

/* text alinment at 1024px */

@media (min-width: 1024px){
.single-popular-post .details p{
  padding-left: 50px;
  font-size: 18px;
}
.single-popular-post h6{
  font-size: 15px;
}
.single-popular-post h4{
  font-size: 20px;
}
}

/* text at mobile view for size M & L*/

@media only screen and (max-width: 375px){
  .single-popular-post .details P{
    font-size: 12px;
  }
}

/* text at tablet view */

@media only screen and (min-width: 768px) and (max-width: 768px){
  .single-popular-post .details P{
    font-size: 16px;
  }
  .single-popular-post h6{
    font-size: 14px;
  }
}

.single-popular-post h4 {
  color: #fff;
}

.single-popular-post h6 {
  padding: 10px 0;
  color: #004d99;
}

.single-popular-post .thumb {
  display: inline-block;
}

.single-popular-post .thumb img {
  margin-top: 10px;
  margin-left: 5px;
}

.single-popular-post .btns {
  background-color: #ff0066;
  color: #fff;
  font-size: 12px;
  position: absolute;
  bottom: 22px;
  font-weight: 400;
  padding: 9px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

/* mobile view for view job post button*/
@media only screen and (max-width: 425px){
  .single-popular-post .btns {
    font-size: 10px;
  }
}

/*New queries*/
@media only screen and (max-width: 425px){
  input{
    height: 40px;
  }
}






/* end of New queries*/



.single-popular-post .btns:hover {
  background-color: #1e2235;
  color: #004d99;
}

.features-area {
  margin-top: -65px;
}

.single-feature {
  text-align: center;
  border-radius: 5px;
  background-color: white;
  box-shadow: 0px 0px 40px 0px rgba(132, 144, 255, 0.2);
  padding: 25px 0;
}

.single-feature p {
  margin-bottom: 0;
}

.single-feature h4 {
  padding-bottom: 15px;
}

@media (max-width: 960px) {
  .single-feature {
    margin-bottom: 30px;
  }
}

.single-fcat {
  text-align: center;
  border-radius: 3px;
  background-color: white;
  box-shadow: 0px 0px 40px 0px rgba(132, 144, 255, 0.2);
  padding: 5px 0;
  border: 1px solid transparent;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-fcat p {
  margin-top: 5px;
  margin-bottom: 0;
  color: #222;
}

.single-fcat:hover {
  border: 1px solid #004d99;
}

@media (max-width: 960px) {
  .single-fcat {
    margin-bottom: 30px;
  }
}

.post-list .cat-list {
  text-align: left;
  margin-bottom: 20px;
}

.post-list .cat-list li {
  text-align: center;
  margin: 2px 10px;
 
  display: inline-block;
  background-color: #fff;
  border: 1px solid #eee;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  padding: 5px 10px;
}

.post-list .cat-list li a {
  text-transform: uppercase;
  font-weight: 600;
  color: #222;
}

.post-list .cat-list li:hover {
  background-color: #004d99;
}

.post-list .cat-list li:hover a {
  color: #fff;
}

@media (max-width: 320px) {
  .post-list .cat-list {
    text-align: left;
  }
  .post-list .cat-list li {
    margin-bottom: 5px;
    font-size: 8px;
    margin-right: 1px;
    margin-left: 5px;
    padding:2px;
    width:60px;
 }


}

@media only screen and (min-width: 330px) and (max-width: 375px){
  .post-list .cat-list li{
    margin: 0px;
    font-size: 8px;
    float: left;
    margin-left: 3px;
    margin-bottom: 5px;
    width: 70px;
  }
}

@media only screen and (min-width: 400px) and (max-width: 425px){
  .post-list .cat-list li{
    margin-bottom: 0px;
    font-size: 10px;
    width: 80px;
    text-align: center;
    margin-right: -35px;
  }
  
}

@media only screen and (min-width: 600px) and (max-width: 768px){
  .post-list .cat-list li{
    margin-right: 10px;
    margin-left: 10px;
    width: 149px;
    font-size: 15px;
  }
  
}


@media(max-width: 1024px)
{
  .post-list .single-post {
  
  background-color: #f9f9ff;

}

}


.post-list .single-post {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 30px;
  border: #E8E8E8 0.25px solid;
  box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.1);
}

.post-list .single-post h6 {
  margin-top: 10px;
  color: #222;
  font-size: 14px;
  font-weight: 300;
  margin-bottom: 15px;
}

@media (max-width: 414px) {
  .post-list .single-post h6 {
    font-size: 10px;
  }
}

.post-list .single-post h5 {
  color: #222;
  font-weight: 300;
  margin-bottom: 15px;
}

.post-list .single-post .address {
  color: #222;
  margin-bottom: 15px;
}

@media(max-width:1024px)

{

.post-list .single-post .btns li {
  
  background-color: #337ab7!important;
  
}


}

.post-list .single-post .btns li {
  display: inline-block;
  background-color: #337ab7!important;
  border: 1px solid #eee;
  padding: 10px 15px;
  margin:0px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.post-list .single-post .btns li i, .post-list .single-post .btns li a {
  text-transform: uppercase;
  font-weight: 500;
 /* color: #222;*/
  color:#fff;
}

.post-list .single-post .btns li:hover {
  background-color: #ff0066!important;
}



.post-list .single-post .btns li:hover a, .post-list .single-post .btns li:hover i {
  color: #fff;
}

@media (max-width: 414px) {
  .post-list .single-post .btns li {
    margin-bottom: 5px;
  }
}

.post-list .single-post .thumb .tags {
  margin-top: 30px;
}

.post-list .single-post .thumb .tags li {
  margin-bottom: 5px;
  display: inline-block;
  background: #fff;
  border: 1px solid #eee;
  padding: 6px 15px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.post-list .single-post .thumb .tags li a {
  color: #777777;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.post-list .single-post .thumb .tags li:hover {
  background-color: #004d99;
}

.post-list .single-post .thumb .tags li:hover a {
  color: #fff;
}

@media (max-width: 736px) {
  .post-list h4 {
    font-size: 16px;
  }
  .post-list .btns a {
    font-size: 10px;
  }
}

@media (max-width: 414px) {
  .post-list .single-post.flex-row {
    flex-direction: column !important;
    display: inline-block !important;
  }
  .post-list .title.flex-row {
    flex-direction: column !important;
    display: inline-block !important;
  }
  .post-list .details {
    margin-top: 30px;
  }
}

.post-list .loadmore-btn {
  text-align: center;
  background-color: #004d99;
  color: #fff;
  padding: 8px 15px;
  margin-top: 60px;
  max-width: 200px;
}

.sidebar .owl-carousel .owl-item img {
  width: auto !important;
}

.sidebar .owl-dots {
  text-align: center;
  bottom: 5px;
  margin-top: 20px;
  width: 100%;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  backface-visibility: hidden;
}

.sidebar .owl-dot {
  height: 10px;
  width: 10px;
  display: inline-block;
  background: rgba(127, 127, 127, 0.5);
  margin-left: 5px;
  margin-right: 5px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.sidebar .owl-dot.active {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  background: #004d99;
}

.sidebar .single-slidebar {
  background-color: #f9f9ff;
  padding: 30px 20px;
  margin-bottom: 30px;
}

.sidebar .single-slidebar h4 {
  margin-bottom: 20px;
}

.sidebar .single-slidebar .cat-list li {
  padding: 0px 10px;
  background-color: #fff;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.sidebar .single-slidebar .cat-list li:hover a {
  color: #fff;
}

.sidebar .single-slidebar .cat-list p {
  margin-bottom: 0;
}

.sidebar .single-slidebar .cat-list a {
  color: #777;
  padding: 10px;
  margin-bottom: 10px;
}

.sidebar .single-rated img {
  margin-bottom: 20px;
}

.sidebar .single-rated h4 {
  margin-bottom: 8px;
}

.sidebar .single-rated h6 {
  font-weight: 300;
  margin-bottom: 10px;
}

.sidebar .single-rated h5 {
  font-weight: 300;
  margin-bottom: 5px;
}

.sidebar .single-rated .address {
  color: #222;
  margin-bottom: 8px;
}

.sidebar .single-rated .btns {
  background-color: #004d99;
  color: #fff;
  border: 1px solid transparent;
  display: inline-block;
  margin-top: 10px;
  padding: 10px 25px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.sidebar .single-rated .btns:hover {
  background-color: transparent;
  border-color: #004d99;
  color: #004d99;
}

.sidebar .single-blog {
  background-size: cover !important;
  padding: 0px 20px;
  margin-bottom: 30px;
}

.sidebar .single-blog:last-child {
  margin-bottom: 0px;
}

.sidebar .single-blog h4 {
  color: #fff;
  padding-top: 20px;
}

.sidebar .single-blog .meta p {
  color: #fff;
}

.callto-action-area .primary-btn {
  margin: 10px;
  text-transform: none;
  border-radius: 0;
  background-color: #004d99;
  color: #fff;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.callto-action-area .primary-btn:hover {
  background-color: #ad9aff;
  color: #fff;
  border-color: transparent;
}

.callto-action-area .overlay-bg {
  background: rgba(4, 9, 30, 0.6);
}

.single-price {
  text-align: center;
  border: 1px solid #eeeeee;
}

.single-price .price-top {
  background-color: #fbfcff;
  border-bottom: 1px solid #eeeeee;
  padding: 30px 0px;
}

.single-price .price-top h4 {
  font-weight: 600;
}

.single-price .price-bottom {
  background-color: #f9f9ff;
  margin: 0 45px 45px 45px;
  padding: 35px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-price .price-bottom h1 {
  font-size: 60px;
  font-weight: 300;
}

.single-price .price-bottom .price {
  font-size: 25px;
  margin-top: 10px;
  color: #222;
}

.single-price .price-bottom .time {
  font-size: 12px;
  text-align: left;
  margin-top: 15px;
  margin-left: 10px;
}

.single-price .price-bottom .primary-btn {
  border-radius: 0;
  margin-top: 20px;
  background: #004d99;
  color: #fff;
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-price .price-bottom .primary-btn:hover {
  border: 1px solid transparent;
}

@media (max-width: 1280px) {
  .single-price .price-bottom {
    margin: 0;
  }

 

}


  


@media (max-width: 1024px) {
  .single-price .price-bottom .primary-btn {
    padding-left: 22px;
    padding-right: 22px;
  }


.sf-arrows .sf-with-ul {
  padding-right: 75px;

}




}

.single-price p {
  padding: 30px 0px;
}

.single-price .lists {
  padding: 40px 0 20px 0;
}

.single-price .lists li {
  margin-bottom: 15px;
}

.single-price:hover {
  cursor: pointer;
}

.single-price:hover .price-bottom h1, .single-price:hover .price-bottom .price, .single-price:hover .price-bottom .time {
  color: #fff;
}

@media (max-width: 960px) {
  .single-price {
    margin-bottom: 50px;
  }
 
}

.download-area {
  background-color: #f9f9ff;
}

@media (max-width: 960px) {
  .download-left {
    margin-bottom: 50px;
  }
}

.download-right {
  padding-left: 65px;
}

.download-right .subs {
  padding: 20px 0;
}

@media (max-width: 1024px) {
  .download-right .flex-row {
    flex-direction: column !important;
    display: inline-block !important;
  }
}

@media (max-width: 960px) {
  .download-right {
    padding-left: 15px;
  }
}

.buttons {
  background: #fff;
  display: flex;
  padding: 20px 30px 0px 30px;
  margin-right: 20px;
  border: 1px solid #eee;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  background-color: #fff;
  color: #222;
}

.buttons i {
  font-size: 40px;
  padding-right: 20px;
  color: #004d99;
}

.buttons:hover {
  cursor: pointer;
  box-shadow: 0px 20px 20px 0px rgba(0, 0, 0, 0.1);
  background: #004d99;
  color: #fff;
}

.buttons:hover i, .buttons:hover a {
  color: #fff !important;
}

.buttons a {
  color: #222;
}

.buttons p span {
  font-size: 24px;
  font-weight: 600;
}

@media (max-width: 1024px) {
  .buttons {
    margin-bottom: 30px;
  }
}

.testimonial-area .owl-carousel .owl-item img {
  width: auto !important;
}

.testimonial-area .owl-dots {
  text-align: center;
  bottom: 5px;
  margin-top: 20px;
  width: 100%;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  backface-visibility: hidden;
}

.testimonial-area .owl-dot {
  height: 10px;
  width: 10px;
  display: inline-block;
  background: rgba(127, 127, 127, 0.5);
  margin-left: 5px;
  margin-right: 5px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.testimonial-area .owl-dot.active {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  background: #004d99;
}

.single-review .title {
  margin: 20px 0;
}

.single-review .star {
  margin-left: 20px;
}

.single-review .star .checked {
  color: orange;
}

@media (max-width: 588px) {
  .single-review {
    padding: 15px;
  }


}


@media(max-width: 1024px)
{

  .footer-area {

  background-color: #004d99;
}
}

.footer-area {
  padding-top: 50px;
  background-color: #004d99;
}

.footer-area .footer-nav li {
  margin-top: 8px;
}

.footer-area .footer-nav li a {
  color: #fff;
}

.footer-area .footer-nav li a:hover {
  color:#ff0066;
}

.footer-area .primary-btn {
  background-color: #ff0066!important;
  line-height: 42px;
  padding-left: 30px;
  padding-right: 60px;
  border-radius: 0px;
  border: none;
  color: #fff;
  display: inline-block;
  font-weight: 500;
  position: relative;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  cursor: pointer;
  text-transform: uppercase;
  position: relative;
}

.footer-area .primary-btn:focus {
  outline: none;
}

.footer-area .primary-btn span {
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-60%);
  right: 30px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.footer-area .primary-btn:hover {
  background-color: #004d99;
  box-shadow: none;
  color: #fff;
}

.footer-area .primary-btn:hover span {
  color: #fff;
  right: 20px;
}

.footer-area .primary-btn.white {
  border: 1px solid #fff;
  color: #fff;
}

.footer-area .primary-btn.white span {
  color: #fff;
}

.footer-area .primary-btn.white:hover {
  background: #fff;
  color: #004d99;
}

.footer-area .primary-btn.white:hover span {
  color: #004d99;
}

.footer-area h6 {
  color: #fff;
  margin-left: 0px;
  margin-bottom: 25px;
  font-size: 18px;
  font-weight: 600;
}

.copy-right-text i, .copy-right-text a {
  color: #004d99;
}

.instafeed {
  margin: -5px;
}

.instafeed li {
  overflow: hidden;
  width: 25%;
}

.instafeed li img {
  margin: 5px;
}

.footer-social {
  text-align: right;
}
.footer-social a {  
  
  width: 40px;
  display: inline-table;
  height: 40px;
  text-align: center;
  padding: 10px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.footer-social a:hover {
  background-color: ##ff0066;
}

.footer-social a:hover i {
  color: #fff;
}

.footer-social i {
  color: #cccccc;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

@media (max-width: 991.98px) {
  .footer-social {
    text-align: left;
    margin-top: 20px;
  }
}

.single-footer-widget {
  color: #fff;

}

.single-footer-widget input {
  line-height: 38px;
  border: none;
  background: #fff;
  font-weight: 300;
  border-radius:0px;
  color: #212529;
  padding-left: 20px;
  width: 100%;
}

.single-footer-widget .bb-btn {
  background-color: #004d99;
  color: #fff;
  font-weight: 300;
  border-radius: 0;
  z-index: 9999;
  cursor: pointer;
}

@media (max-width: 960px) {
  .single-footer-widget .nw-btn {
    margin-top: 20px;
  }
  .single-footer-widget .newsletter {
    padding-bottom: 40px;
  }
}

.single-footer-widget .info {
  position: absolute;
  margin-top: 122px;
  color: #fff;
  font-size: 12px;
}

.single-footer-widget .info.valid {
  color: green;
}

.single-footer-widget .info.error {
  color: red;
}

.single-footer-widget ::-moz-selection {
  /* Code for Firefox */
  background-color: #004d99;
  color: #777777;
}

.single-footer-widget ::selection {
  background-color: #004d99;
  color: #777777;
}

.single-footer-widget ::-webkit-input-placeholder {
  /* WebKit, Blink, Edge */
  color: #777777;
  font-weight: 300;
}

.single-footer-widget :-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #777777;
  opacity: 1;
  font-weight: 300;
}

.single-footer-widget ::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #777777;
  opacity: 1;
  font-weight: 300;
}

.single-footer-widget :-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #777777;
  font-weight: 300;
}

.single-footer-widget ::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #777777;
  font-weight: 300;
}

@media (max-width: 991.98px) {
  .single-footer-widget {
    margin-bottom: 70px;
  }
}

.footer-bottom {
  padding-top: 35px;
  padding-bottom:1px;

}

.footer-text a, .footer-text i {
  color: #004d99;
}

.whole-wrap {
  background-color: #fff;
}

.generic-banner {
  background-color: #004d99;
  text-align: center;
}

.generic-banner .height {
  height: 600px;
}

@media (max-width: 767.98px) {
  .generic-banner .height {
    height: 400px;
  }
}

.generic-banner .generic-banner-content h2 {
  line-height: 1.2em;
  margin-bottom: 20px;
}

@media (max-width: 991.98px) {
  .generic-banner .generic-banner-content h2 br {
    display: none;
  }
}

.generic-banner .generic-banner-content p {
  text-align: center;
  font-size: 16px;
}

@media (max-width: 991.98px) {
  .generic-banner .generic-banner-content p br {
    display: none;
  }
}

.generic-content h1 {
  font-weight: 600;
}

.about-generic-area {
  background: #fff;
}

.about-generic-area p {
  margin-bottom: 20px;
}

.white-bg {
  background: #fff;
}

.section-top-border {
  padding: 70px 0;
  border-top: 1px dotted #eee;
}

.switch-wrap {
  margin-bottom: 10px;
}

.switch-wrap p {
  margin: 0;
}

.link-nav {
  margin-top: 10px;
}

.about-content {
  padding: 1px 0px 1px 0px;
  text-align: center;
}

.about-content h1 {
  font-size: 38px;
  font-weight: 300;
}

.about-content a {
  color: #fff;
  font-weight: 300;
  font-size: 14px;
}

.about-content .lnr {
  margin: 0px 10px;
  font-weight: 600;
}

.feature-area {
  background-color: #222;
}

.feat-img img {
  width: 100%;
}

.feat-txt {
  padding: 0px 70px;
}

.feat-txt h6 {
  font-weight: 100;
  letter-spacing: 2px;
}

.feat-txt h1 {
  padding: 15px 0;
}

@media (max-width: 1280px) {
  .feat-txt {
    padding: 0px 12px;
  }
  .feat-txt h1 {
    font-size: 30px;
  }
}

@media (max-width: 1024px) {
  .feat-txt h6, .feat-txt p {
    font-size: 12px;
  }
  .feat-txt p {
    margin: 0;
    padding: 0;
  }
  .feat-txt h1 {
    font-size: 24px;
    padding: 5px 0;
  }
}

@media (max-width: 960px) {
  .feat-txt {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

.service-area .header-text {
  text-align: center;
}

.service-area .header-text h1 {
  margin-bottom: 20px;
}




.single-service {
  background-color:#F8F8F8;
    /*#FFFFF0*/
  padding: 15px!important;
  margin-bottom: 30px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
   border-color:grey;
    border: 1px;
    border-style:inset;
    box-shadow: 0.5px 5px #D3D3D3;
}

.single-service-recruitment {
  background-color:#F8F8F8;
/**/

  padding: 15px!important;
  margin-bottom: 30px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
   border-color:grey;
    border: 1px;
    border-style:inset;
    box-shadow: 0.5px 5px #D3D3D3;
}


.single-service-recruitment:hover {
  box-shadow: -14.142px 14.142px 20px 0px rgba(157, 157, 157, 0.5);
  cursor: pointer;
}

.single-service-recruitment:hover h4, .single-service:hover p, .single-service:hover .lnr {
  color: #fff;
}

single-service-recruitment p {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-service-recruitment h4 {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  margin-top: 20px;
  margin-bottom: 20px;
}

.single-service-recruitment h4 .lnr {
  margin-right: 15px;
}









.single-service:hover {
  box-shadow: -14.142px 14.142px 20px 0px rgba(157, 157, 157, 0.5);
  cursor: pointer;
}

.single-service:hover h4, .single-service:hover p, .single-service:hover .lnr {
  color: #fff;
}

.single-service p {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-service h4 {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  margin-top: 20px;
  margin-bottom: 20px;
}

.single-service h4 .lnr {
  margin-right: 15px;
}

.team-area .thumb {
  position: relative;
}

.team-area .thumb div {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(129, 104, 255, 0.8);
  color: #fff;
  opacity: 0;
  transition: opacity 0.5s;
}

.team-area .thumb div i {
  color: #fff;
  font-size: 20px;
  padding: 10px;
  z-index: 9999;
}

.team-area .thumb img {
  display: block;
  width: 100%;
}

.team-area .thumb div span {
  display: block;
  position: absolute;
  bottom: 30px;
  left: 20px;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 3px;
}

.team-area .thumb div p {
  display: block;
  position: absolute;
  bottom: 10px;
  left: 20px;
  font-weight: 100;
}

@media (max-width: 768px) {
  .team-area .thumb div p {
    bottom: -15px;
  }
}

.team-area .thumb:hover div {
  cursor: pointer;
  opacity: 1;
}

.single-title {
  margin-bottom: 20px;
}

.job-experience ul li {
  color: #004d99;
  margin-bottom: 30px;
}

.job-experience ul li span {
  color: black;
}

.search-page-top {
  padding: 250px 0px;
}

.search-page-top a, .search-page-top .lnr {
  color: #fff;
}

.search-page-top .lnr {
  font-weight: 700;
  margin: 0px 10px;
}

.single-price {
  text-align: center;
  border: 1px solid #eeeeee;
}

.single-price .price-top {
  background-color: #fbfcff;
  border-bottom: 1px solid #eeeeee;
  padding: 30px 0px;
}

.single-price .price-top h4 {
  font-weight: 500;
}

.single-price .price-bottom {
  background-color: #f9f9ff;
  margin: 0 45px 45px 45px;
  padding: 35px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-price .price-bottom h1 {
  font-size: 60px;
  font-weight: 300;
}

.single-price .price-bottom .price {
  font-size: 25px;
  margin-top: 10px;
  color: #222;
}

.single-price .price-bottom .time {
  font-size: 12px;
  text-align: left;
  margin-top: 15px;
  margin-left: 10px;
}

.single-price .price-bottom .primary-btn {
  border-radius: 0;
  margin-top: 20px;
  background: #004d99;
  color: #fff;
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single-price .price-bottom .primary-btn:hover {
  border: 1px solid transparent;
}

@media (max-width: 1280px) {
  .single-price .price-bottom {
    margin: 0;
  }
}

@media (max-width: 1024px) {
  .single-price .price-bottom .primary-btn {
    padding-left: 22px;
    padding-right: 22px;
  }
}

.single-price p {
  padding: 30px 0px;
}

.single-price .lists {
  padding: 40px 0 20px 0;
}

.single-price .lists li {
  margin-bottom: 20px;
}

.single-price:hover {
  cursor: pointer;
}

.single-price:hover .price-bottom {
  /*background-color: #004d99;*/

  background-color:#FF4500;
}

.single-price:hover .price-bottom h1, .single-price:hover .price-bottom .price, .single-price:hover .price-bottom .time {
  color: #fff;
}

@media (max-width: 960px) {
  .single-price {
    margin-bottom: 50px;
  }
}

.submit-right, .submit-left {
  color: #fff;
  text-align: center;
  padding: 50px 30px;
}

.submit-right h4, .submit-left h4 {
  color: #fff;
}

.submit-right p, .submit-left p {
  padding: 20px 0;
}

.map-wrap {
  margin-bottom: 120px;
}

.contact-btns {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  padding: 15px;
  color: #fff;
  margin: 0px 7px 30px;
}

.contact-btns:hover {
  color: #fff;
}

.form-area input {
  padding: 15px;
}

.form-area input, .form-area textarea {
  border-radius: 0;
  font-size: 12px;
}

.form-area textarea {
  height: 180px;
  margin-top: 0px;
}

.form-area .primary-btn {
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.form-area .primary-btn:hover {
  border: 1px solid #004d99;
  color: #004d99 !important;
}


/*
################
               Start Blog Home  Page style
################
*/
.blog-banner-area {
  background: url(../img/elements/blog/blog-banner.jpg) center;
  background-size: cover;
}

.blog-banner-area .overlay-bg {
  background: rgba(0, 0, 0, 0.55);
}

.blog-banner-wrap {
  text-align: center;
}

.blog-post-list .single-post {
  background: transparent;
  padding: 0px 30px;
}

.single-post {
  margin-bottom: 30px;
}

.single-post .tags {
  margin-top: 40px;
}

.single-post .tags li {
  display: inline-block;
}

.single-post .tags li a {
  color: #222;
}

.single-post .tags li:hover a {
  color: #004d99;
}

.single-post h1 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.single-post .title h4 {
  margin-bottom: 10px;
}

.single-post .comment-wrap ul li, .single-post .social-wrap ul li {
  display: inline-block;
  margin-right: 15px;
}

@media (max-width: 414px) {
  .single-post .comment-wrap, .single-post .social-wrap {
    margin-top: 10px;
  }
}

.single-post .comment-wrap ul li a {
  color: #777777;
}

.single-post .social-wrap ul {
  text-align: right;
}

.single-post .social-wrap ul li a i {
  color: #777777;
}

.single-post .social-wrap ul li a i:hover {
  color: #222;
}

@media (max-width: 768px) {
  .single-post .social-wrap ul {
    text-align: left;
  }
}

.single-widget {
  border: 1px solid #eee;
  padding: 40px 30px;
  margin-bottom: 30px;
}

.single-widget .title {
  font-weight: 600;
  margin-bottom: 30px;
}

.search-widget i {
  color: #004d99;
}

.search-widget form.example {
  border: 1px solid #eee;
}

.search-widget form.example input[type=text] {
  padding: 10px;
  font-size: 14px;
  border: none;
  float: left;
  width: 80%;
  background: #eee;
}

.search-widget form.example button {
  float: left;
  width: 20%;
  padding: 10px;
  background: #eee;
  color: white;
  font-size: 17px;
  border: none;
  cursor: pointer;
}

.search-widget form.example::after {
  content: "";
  clear: both;
  display: table;
}

.protfolio-widget {
  text-align: center;
}

.protfolio-widget h4 {
  padding: 20px 0;
}

.protfolio-widget ul li {
  padding: 15px;
  display: inline-block;
}

.protfolio-widget ul li a i {
  color: #222;
}

.protfolio-widget ul li a i:hover {
  color: #004d99;
}

.category-widget ul li {
  border-bottom: 1px solid #eee;
  padding: 10px 20px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.category-widget ul li h6 {
  font-weight: 300;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.category-widget ul li span {
  color: #222;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.category-widget ul li:hover {
  border-color: #004d99;
}

.category-widget ul li:hover h6, .category-widget ul li:hover span {
  color: #004d99;
}

.tags-widget ul li {
  display: inline-block;
  background: #fff;
  border: 1px solid #eee;
  padding: 8px 10px;
  margin-bottom: 8px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.tags-widget ul li a {
  font-weight: 300;
  color: #222;
}

.tags-widget ul li:hover {
  background-color: #004d99;
}

.tags-widget ul li:hover a {
  color: #fff;
}

.recent-posts-widget .single-recent-post .recent-details {
  margin-left: 20px;
}

.recent-posts-widget .single-recent-post .recent-details h4 {
  line-height: 1.5em !important;
  font-size: 14px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.recent-posts-widget .single-recent-post .recent-details p {
  margin-top: 10px;
}

.recent-posts-widget .single-recent-post:hover h4 {
  color: #004d99;
}

@media (max-width: 960px) {
  .recent-posts-widget .single-recent-post {
    margin-bottom: 20px;
  }
}

/*
################
               End Blog Home  Page style
################
*/
/*
################
               Start Blog Details  Page style
################
*/
.nav-area {
  border-bottom: 1px solid #eee;
}

.nav-area a {
  color: #222;
}

.nav-area .nav-left .thumb {
  padding-right: 20px;
}

.nav-area .nav-right {
  text-align: right;
}

.nav-area .nav-right .thumb {
  padding-left: 20px;
}

@media (max-width: 768px) {
  .nav-area .post-details h4 {
    font-size: 14px;
  }
}

@media (max-width: 466px) {
  .nav-area .nav-right {
    margin-top: 50px;
  }
}

.comment-sec-area {
  border-bottom: 1px solid #eee;
  padding-bottom: 50px;
}

.comment-sec-area a {
  color: #222;
}

.comment-sec-area .comment-list {
  padding-bottom: 30px;
}

.comment-sec-area .comment-list.left-padding {
  padding-left: 25px;
}

.comment-sec-area .thumb {
  margin-right: 20px;
}

.comment-sec-area .date {
  font-size: 13px;
  color: #cccccc;
}

.comment-sec-area .comment {
  color: #777777;
}

.comment-sec-area .btn-reply {
  background-color: #222;
  color: #fff;
  border: 1px solid #222;
  padding: 8px 30px;
  display: block;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.comment-sec-area .btn-reply:hover {
  background-color: transparent;
  color: #222;
}

.commentform-area {
  padding-bottom: 100px;
}

.commentform-area h5 {
  font-weight: 600 !important;
}

.commentform-area .form-control {
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
}

.commentform-area .primary-btn {
  padding-left: 50px;
  padding-right: 50px;
  background-color: #004d99 !important;
  border: 1px solid transparent;
  color: #fff;
}

.commentform-area .primary-btn:hover {
  border-color: #004d99 !important;
}

.commentform-area textarea {
  height: 90%;
}

.commentform-area .form-control {
  border: none;
  background: #f9f9ff;
}




/* Nav menu 2 starts */



@media (max-width:1024px )
 {

.nav2Align  a { 
      display: inline-block; 
      /*background: #1c87c9; */
     /* margin-right: 10px;*/
     /* color:red; */
      font-weight:bold; 
     /* margin-left:-90px;*/
      text-decoration:none; 
           
 }

.nav2Align  li  {
 
  /*width: 40px;*/
  font-size:12px; 
   
    
 }





/*Menu 2 */
.nav-menu{
  width: 10px;
  font-size:12px; 
display:inline;
}


.nav-menu li a{

    width: 60px;
    margin-right: 1px;
    margin-left: 5px;
    font-size: 10px;
}


}


/* Default large laptop*/
.nav2Align{
   display:inline;
   padding-left:40px;
   margin:  -20px 0px 4px -90px;


 }


/*li  {

margin-right: 25px;
margin-left:35px;

}*/

li>a:hover {
  color: blue;
;
}

li>a{color: #A9A9A9;
        }


/*Nav menu 2 ends*/



/*Updated footer css links*/
#app {
    background-color: #fff;
    color: black;
    width: 100%;
    text-align: justify;
    margin-right: 100px;

}


@media (min-width:1024px) and (max-width:1024px){
 

  .footer-social a{
    padding: 5px;
    float: left;
  }


  .footer-area .primary-btn {

  padding-right:45px;

  }

}

/*Updated job ads css */

.image{
  height: 100px;
  width: fit-content;
  margin-left: -210px;
}

#image{
  margin-top: 10px;
}

.desp{
  text-align: justify;
   white-space: nowrap; 
  width: 470px; 
  overflow: hidden;
  text-overflow: ellipsis; 
}

.locate{
  margin-left: 30px;
  font-size: 18px;
}

#view{ 
  background-color:#fff!important;
  border: none;

}

.pagei{
    padding: 20px 23px;
    border:1px solid black;
    color:black;
}

.pagei:hover{
   background-color: #ff0066;
   color:#fff;
}


  #pagei:active>a{
    
    background-color:#337ab7!important; 
  }


  @media(max-width:2560px) and (min-width:768px){

.loadmore{ visibility: hidden!important; }

}



@media (max-width:425px){
    #pagei{
    display:none;
  }
}

@media (min-width:1024px) and (max-width:1024px){

.image{
      height: 100px;
      width: 180px;
      margin-left: -190px;
    }

  .desp{
      text-align: justify;
      white-space: nowrap; 
      width: 370px; 
      overflow: hidden;
      text-overflow: ellipsis; 
    }

    #image{
      margin:10px 0px 0px 0px;
    }

    .locate{
    margin-left: 13px;
  }

  .pagei{
    padding: 15px 17px;
    border:1px solid black;
  }

}

@media (min-width:768px) and (max-width:768px){

  .image{
      height: 100px;
      width: fit-content;
      margin-left: -230px;
    }
  .desp{
      text-align: justify;
      white-space: nowrap; 
      width: 430px; 
      overflow: hidden;
      text-overflow: ellipsis; 
    }

    #image{
      margin:10px 0px 0px 20px;
    }

    .pagei{
    padding: 18px 21px;
    border:1px solid black;
  }



}

@media (min-width:425px) and (max-width:425px){
  .desp{
      text-align: justify;
      white-space: nowrap; 
      width: 255px; 
      overflow: hidden;
      text-overflow: ellipsis; 
    }

    .locate{
      margin-left: 15px;
    }

     #image{
      margin:0px;
    }

    .image{
      height: 80px;
    }

     .none{
      display: none;
    }
}

@media (min-width:375px) and (max-width:375px){
  .desp{
      text-align: justify;
      white-space: nowrap; 
      width: 270px; 
      overflow: hidden;
      text-overflow: ellipsis; 
    }

    .locate{
      margin-left: 20px;
    }
    
    .none{
      display: none;
    }

}

@media (min-width:320px) and (max-width:320px){
  .desp{
      text-align: justify;
      white-space: nowrap; 
      width: 225px; 
      overflow: hidden;
      text-overflow: ellipsis; 
    }

    .locate{
      margin-left: 20px;
    }
    
    .none{
      display: none;
    }

}

#bnr11{
  margin-bottom: 0px;
}


/*Search Page*/

#sidebar{
  padding: 10px 20px;
  margin-bottom: 10px;
}

#ntb{
  width: 15px;
  height: 30px;
}



@media (min-width: 768px) and (max-width: 768px){
  #list{
    margin-right: 0px;
    margin-left: 0px;
  }
}



#min{
  background-color:#fff; 
  height: 40px; 
  width: 355px;
}

@media (min-width: 1024px) and (max-width: 1024px){
  #min{

    height: 40px; 
    width: 290px;
  }
}

@media (min-width: 768px) and (max-width: 768px){
  #min{
    height: 40px; 
    width: 355px;
    margin-bottom: 1px;
    margin-left: 170px;
  }
}

@media (min-width: 425px) and (max-width: 425px){
  #min{    
    margin-bottom: 1px;    
  }
}

@media (min-width: 325px) and (max-width: 375px){
  #min{
    height: 40px; 
    width: 305px;
    margin-bottom: 1px;    
  }
}



/*end of Updated job ads css */

/*apply page*/

.thisjob{
  background-color:#337ab7!important;
  color:white;
  width: -webkit-fill-available; 
}

#thisjob:hover{
  color:#ff0066;
}

.save{
  background-color:#337ab7!important;
  color:white;
  margin-top:10px;   
   width: -webkit-fill-available;
}

#cimg{
  height:110px;
}

@media (min-width:425px) and (max-width:425px){
    #cimg{
    float: right;
  }
}

.ali{
  margin-bottom: 10px;
  margin-right: 0px;
  margin-left: 0px;
}

.load{
  float:right;
  color:#337ab7;
  font-weight: 600;
}

.asocial{
  background-color:#337ab7!important;
  color:white;
}

#aimg{
  padding-left:54%;  
}

@media(max-width:1024px){
    #aimg{
    padding-left:48%;    
  }  
}

@media(max-width:768px){
    #aimg{
    padding-left:35%;    
  }  
}

@media (min-width:320px) and (max-width:425px){
    #aimg{
    margin-top:5px;        
    padding-left: 40px;
  }  
}

@media (min-width:320px) and (max-width:375px){
    #cdetails{
    margin-top:0px;    
  }  
}

/*end of apply job css */

/*start of more job css */
#moresidebar{
  text-align: center;
}

.mimage{
  margin-bottom:10px;
  height: 100px;
  width: 200px;
}

@media (max-width:1024px){
    .mimage{  
    width: 160px;
  }  
}

@media(max-width:768px )
{

  #displayNoneMobile{

    display:none;
  }
}



/*start of business for sale css */

@media (max-width:425px){
  #business{
    display:none;
  }
}

@media (min-width:435px){
  .businessfilter{
    display: none;
  }
}

@media (min-width:1000px){
  .businessimage{
    height: 150px;
    margin-left: -130px;
    margin-top: 40px;
  }
}





@media (min-width:700px) and (max-width:800px){
 
  .businessimage{
    height: 140px;
    margin-left: -215px;
    margin-top: 55px;
    width: fit-content;
  }
}

@media (min-width:425px) and (max-width:425px){
  .businessimage{
    height: 80px;
  }
}

.pageb{   
    padding: 10px 14px;
    border:1px solid black;
    color:black;
}

.pageb:hover{
   background-color: #ff0066;
   color:#fff;
}

@media (min-width:768px) and (max-width:768px){
  .pageb{
    padding: 20px 21px;
  }
}

@media (min-width:1020px) and (max-width:1024px){
  .pageb{
    padding: 8px 10px;
  }
}

#businesscomp{
  background-color: #337ab7;
  color:white;
  width:-webkit-fill-available;
  border-radius: 0px;
}

#businesscomp:hover{
  background-color:  #ff0066!important;
  color:white; 
}



.post_ad{
  background-color: #337ab7!important;
  color: white!important;
}

.post_ad:hover{
  background-color: #ff0066!important;
  color: white!important;
}


.card-header{
  background-color: #1a75ff!important;
  color: white!important;
}

/*index css changed*/

.indeximage{
    height: 110px;
    width: 160px;
    margin-left: -175px;
  }

  

@media (min-width:1000px) and (max-width:1024px) {
  .indeximage{
    height: 110px;
    width: 120px;
    margin-left: -140px;
  } 
}


@media (min-width:700px) and (max-width:800px){
  .indeximage{
    height: 110px;
    width: 155px;
  }
}

@media (min-width:425px) and (max-width:425px){
  .indeximage{
    height: 240px;
    width:160px;
  } 
}
.pagein{   
    padding: 10px 14px;
    border:1px solid black;
    color:black;
}

.pagein:hover{
   background-color: #ff0066;
   color:#fff;
}

@media (min-width:768px) and (max-width:768px){
  .pagein{
    padding: 20px 21px;
  }
}

@media (min-width:1020px) and (max-width:1024px){
  .pagein{
    padding: 8px 10px;
  }
}

@media (min-width:320px) and (max-width:430px){
  .pagein{
    display: none;
  }
}

/*index css changed*/

/*end of business for sale css */


/*featured categories area css added*/
    #imgfcat{
      width:35px;
    }



/*fcat area links css added*/


/*top-navigation css added*/
    #links{
      padding: 0px 18px;
    }

     @media (min-width:1024px) and (max-width:1024px){
      #links{
      padding: 0px 10px;
      }

      #navAlign{
        margin-left: 90px;
      }
  } 
/*top links css added*/



/*footer css added*/
  .footer-margin{
    margin: 0px 19px;
  }

  @media (min-width:1024px) and (max-width:1024px){
      .footer-margin{
        margin: 0px 16px;
      }

      .footer-area .footer-nav li a {
        font-size: 12px;
      } 

      #imgCareers{
      width:400px;
      height:250px;
      padding-right:10px; 
    } 
  } 
/*footer css added*/


 /* Service Image added*/   

 #imgService{
      width:120px;
      height:80px;
      padding-right:10px; 
    }

#imgCareers{
      width:1000px;
      height:250px;
      padding-right:10px; 
    }




.single-fcat:hover {
  box-shadow: -14.142px 14.142px 20px 0px rgba(157, 157, 157, 0.5);
  background-color: #F8F8F8;

  cursor: pointer;
}


@media (max-width: 1024px) and (min-width: 1024px){
#imgService{
      width:100px;
      height:40px;
      padding-right:10px; 
    }


#imgCareers{
      width:1000px;
      height:300px;
      padding-right:10px; 
    }


   .fh5 { font-size:10px!important; 
             text-align:left;
           font-weight:bold!important;

    }
          }

}

/* Bootstrap 4 text input with search icon */

.has-search .form-control {
    padding-left: 3.375rem;
}

.has-search .form-control-search {
    position: absolute; 
    margin-left:10px;
    display: block;
    width: 70.375rem;
    height: 2.375rem;
    line-height: 2.375rem;
   
    pointer-events: none;
    color: #aaa;
    padding-top:5px;
}

.has-search .btn{
    background-color: #4a90e2;
    color: #fff;
}   

.has-search .btn{
    background-color: #4a90e2;
    color: #fff;
}

.advice{
    background-color: #4a90e2;
    color: #fff;
}
/* End of career advise search*/  
.resume>li>a{
  color:#004d99!important;
  font-size: 16px;
}

#resume{
  margin-bottom: 10px;
}

/*career advise search*/

  .main {
    width: 50%;
    margin: 0px auto;
}


#service-btn{
  margin-bottom: 20px;
  width: -webkit-fill-available;
}

.btn-active{
  background-color: #ff0066! important;
  color:#fff! important;
}
/*End Resume Templates Quick Links*/ 


/*Careers css update*/ 

.width-fill{
  width: -webkit-fill-available;
}

#over{
  border: 1px solid gray;
   width: -webkit-fill-available;
}

#single-service{
  background-color: #f9f9ff;
  padding: 10px;
  margin-bottom: 20px;  
}

#single-service:hover b{
  color:#fff;  
  text-decoration: none;
}

#single-service:hover h4{
  color:#fff;  
  text-decoration: none;
}

#imgcareer{
  width:100%;
  height:150px;
}

#imgcareerit{
  width:100%;
  height:189px;
}

@media (max-width:1024px) and (min-width:768px){
    .imageover{
      width:170px;
    }
}

/*End careers css*/ 


/*
################
               End Blog Details  Page style
################
*/
